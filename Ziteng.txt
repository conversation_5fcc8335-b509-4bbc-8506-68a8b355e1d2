Transcript
May 10, 2025, 9:06AM

<PERSON> started transcription

<PERSON>   0:03
The entire DT team is on a very tight schedule at the beginning of the connect period, we have our distributed tracing platform ready with single trace view. The aggregation view and dependency map built for.
Copilot scenario.
And the tone we set for this connect period is we still want to keep certain bandwidth on the copilot scenario enhancement, like connecting new customers and.
Accept and response.
To requirements coming from 3S team.
And at the meantime, we're gradually shifting our focus from.
Copilot scenario to back to substrate and back to red alert scenario.
Now we are happy to see that in copilot scenario we changed the situation where we build a solution. We release the product but customer is not proactively using our system.
With the major wave of leveraging OIC technology to benefit 3S teams.
Diagnosis 3S team is.
Using distributed tracing tools that we build.
And.
More positive.
Away.
And inevitably, we do receive a lot of requirements to refine or even refactor our system at the same time, we also had a lot of challenge.
Including.
One of the challenge is with more and more service onboard to DTP and the traffic volume is growing larger and larger. Not to mention we're also expanding to altering which could bring more traffic and more services.
The reliability of the product become a risk and challenge.
Whenever we have some issue with our pipeline with our UI and our back end, it will directly impact customer.
And we do.
Have.
A very painful memory about the incidents we went into, which alerts us to be well prepared to have a service mindset where we are maintaining a product that is live and serve real customer.
We need to treat the our service, SLA and reliability really seriously.
Otherwise it will harm the reputation of the product and our team.
For that purpose, we.
Took a step back and look at how we orchestrate our build and release process, how we monitor the healthiness of the system and how we can.
Proactively avoid issue from happening rather than react after the issue.
Impact the customer that brings a lot of challenge and.
Also, it helps us build a more reliable and robust system.
Another challenge is.
Actually part of the challenge I said before when we say the system healthiness, it not only includes functionality but also performance.
Our code pass distributed tracing platform is based on Cosmos platform and scope script.
Which?
Needs a lot of tuning to reach the sweet spot of performance.
With the traffic adjustment, we do have a lot of challenges that the job that we use to cook the trace data.
Need to run too long which go way beyond our SLA.
So we have a lot of effort to reduce.
That job duration by optimizing scope script, optimizing the schema to make the entire system within our predefined SLA.
For the town's contribution, I really appreciate he contributed a lot to our success for this semester.
First of all.
Ziteng was part of the the core team that builds the UX experience for our DTP.
And also he's always the 1st to he's always.
They always have a very fast reaction and take action very also very, very fast when faced with some customer ask or some PM ask for example.
Drove the optimization of single trace view like supporting the critical path.
Adding the single trace view dependency map to the single Trace view page supporting.
Virtual nodes to align our calculation. Our latency calculation with layer cake calculation and a lot of optimization on the UI that's make the user experience more friendly.
Meantime, Zitong is also taking responsibility of the latency aggregation view. Not only the UI, but also the job behind. I'm very happy to see that Ziton is taking responsibility of a senior member of the team. Not only did he.
Suggested and carried out a lot of optimization to existing solution.
It can also think innovatively to.
Based on his.
Connection with customers.
He could propose new solution doing Mvps on his proposal.
And and finally carry the solution out.
During this process, I do see he very proactively get connected with his tech lead.
With engineering managers and PMS to collect feedback and hosted a lot of.
Brainstorm and Design review and design discussions to make sure his new proposal.
Does it raise any concern?
Also, he helped to tackle a lot of issues including the UI issue where single trace view took too much time to load.
And also the.
And also the displaying issue of certain dimensions on a single trace view.
Another highlight I want to emphasize is that it also has a lot of knowledge in the scope job optimization.
He working together with his team.
Did a lot of improvements on the job we have reducing the job running time and optimizing the job triggering mechanism.
Eventually get the job running duration and.
Get the job running duration in a reasonable time.
Apart from this huge achievement, Zhou Tong also contributed to a lot of.
Issues a lot of tasks, including.
Building the DAU and Mau report, fixing some ad hoc bugs.
Get connected to 3S team to optimize our UI and experience.
In short.
The tone's work is very sound.
He's very fully qualified for the expectation of a senior member apart from his IC contribution. I also want to call out his.
Excellent teamwork and driver role inside the team.
He was.
Assigned a driver and tech lead for performance improvement.
Where he have very close collaboration with another tech lead inside distributed tracing team.
I see he can.
Play as a team.
Try to have good arrangement of resources under him and make sure he has a solid plan on the week to week and month to month basis and make sure the delivery is on time and with high quality.
Also, he could provide a lot of suggestion to team members.
Not even in his team.
And he's also helpful and help create a very open and proactive team culture.
Overall, I think the tone is a essential.
Member of the team and his contribution to the team is not only in his own knowledge and skill, but also he helped make the team more efficient and set a very good example of what we expect for a senior member.
Finally, for those improvement.
I do think that in DTT also Don mentioned in his challenge and set back.
I actually see a lot of opportunities inside DT team where a lot of manual effort takes too many time.
And Ziteng actually make a very good point of embracing AI, as I said.
AI tool shouldn't be treated as some fancy stuff or just a fancy.
Toys that people can play with. Instead, I would really encourage everyone to try whatever AI tools you can use during your daily work.
Based on my recent observation, things like converting some Jason to C# library or building some pilot UI even locally to quickly verify your algorithm or.
Figuring out and describe the API call chain in a new project which you didn't have knowledge at all.
This this kind of work can be accelerated with AI tools from the original hours or even days to minutes.
So that is a.
That is.
A.
I think that is not only beneficial for the team and Microsoft, but also beneficial to your long term career as a SDE.
The industry changes swiftly, so make sure everyone follow this trend.
And for the tones selfreflection on his English skill.
My observation is.
He did get a lot of experience in how to present and how to prepare for the presentation.
I do appreciate his sharing on the.
DT team internal session.
And a suggestion to is, uh, first do more practice.
Use whatever opportunity you can find to share what you learned, or even share some problem that is remaining unsolved, and seek suggestions from other people's. And the second suggestion is make sure every time you do a presentation, especially for English presentation, make sure you are well, prep.
You can even write your transcript before you do that.
And when you get more fluent, maybe you can get rid of the transcript and.
And and carry out your presentation in a more confident and fluent manner.

Kyle Zhao stopped transcription
