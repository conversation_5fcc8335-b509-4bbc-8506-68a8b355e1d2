Transcript
May 11, 2025, 3:20AM

<PERSON> started transcription

Kyle <PERSON>   0:06
This semester has been a very stressful and fruitful semester for TTT.
In this team we have fantastic progress in copilot scenario.
At the same time, we also explored or returned to our previous effort in red alert scenario in substrate.
In copilot scenario, I'm happy to see that we're no longer playing with ourselves.
With the three UX we build, we see that.
3S Team due to the huge wave of adopting OSC technology to improve their observability.
They're also willing to.
Try DT tools out and gave us a lot of feedback.
And also there are different teams that is relying on <PERSON><PERSON>'s data experience and insights and try to derive their own insights.
For example AB deployment and Fillspot team.
With this kind of progress, we do have more users and at the same time it push us to shift from a solution team to a production team, meaning that we should take full responsibility.
Of the SLA, the robustness and reliability of our service that we release to the customer.
Also, we do have to plan to expand the scope of DT to altering and to more services and all this together adds a lot of stress on the data volume, the throughput and the performance of our pipelines and Uis.
So.
Back to red alert, I think that is the effort we.
Went halfway before and now we just resume that effort and with more experience and more learnings from copilot scenario, we have a chance to step back and take another look at the previous approach.
We found that there are some limitations or some issues.
We didn't see in the previous approach, including.
Higher requirements to the data quality to the trace integrity and.
Also to the.
Fine grained ground truth of each incident.
So altogether I think.
There are a lot of challenges in front of this semester and also we can expect more impact coming out from DT team.
So about Ying's contribution, I think first of all.
She at the beginning of the semester.
The XAP connectivity issue was a huge blocker for our copilot scenario.
Almost for everything in copilot scenario and.
What is really impressing to me is in not only.
Innovatively, think of different workarounds. She can systematically summarize her ideas and approaches together with her teammates to come up with different options and analyze their pros and cons.
And she actively joined our communication with 3S stakeholders and eventually.
And also she she she take action very fast.
Our communication was never blocked by her code fix.
Whenever.
She gets a comment or suggestion from our partners. She can take action immediately. That is really impressing.
Apart from that, another big achievement is.
She helped investigate a lot of code quality and trace connectivity issue.
Such kind of issues are rooted in services that we don't own.
And trying to figure out ways certain dimension is missing or why certain spans are parentless.
And requires some quick and thorough understanding of a code base that we didn't have knowledge before that will be a very huge challenge and it can happen on very different services and I'm really impressed that.
You can.
Very quickly, get familiar with the code base and find what you want inside the code base and come to conclusion and propose the the fix.
Apart from that, I think previously we talked that always smart people is not so diligent, but I think that's not true on you.
One concrete example is when we did the 3S incidents analysis.
You have.
Spent months analyzing incidents on a weekly basis where you summarize the good incidents, bad incidents incident theoretically should be mitigated or helped with DT and you summarize.
Report also on a weekly basis. I think that kind of summary, although we didn't have.
An engagement with 3S team as close as we have now, but that kind of that's the first step that we try to use incident analysis to prove the Dt's value.
I know that is not a easy work and dealing with different incidents and talking with different OCE and stakeholders to figure out the real root cause, but you actually did it.
So.
And apart from that, let's talk about red alert.
Umm.
For Red alert, most of the services are more familiar to us and is owned by substrate.
I'm happy to see that.
You helped mitigate a lot of the subject specific trace disconnection issue, wrong status issue and wrong dimension issue.
And get it fixed very fast to unblock our red alert routing.
Apart from your IC work, I'm also happy to see your growth in.
Your technical leadership, I think for.
For the instant analysis.
You proactively reach out to different people and try to get suggestions and feedback for your proposal and also for the difficult XAP fix issue.
You also worked very closely with different team members and your tech leads.
To figure out the best solution.
I think you could improve a little bit more in the future.
Contributing more to other people's work, providing your suggestion and your insight, and let people know what you're thinking and let people know that you can help.
That could really help you build your technical leadership and also your population.
Which is helpful for you going to next career stage.

Kyle Zhao stopped transcription
